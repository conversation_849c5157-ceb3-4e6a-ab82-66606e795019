cl_config = {}

cl_config.settings = {
    renderDistance = 25.0,
}

cl_config.positions = {
    {
        position = vector3(-1081.9529, -247.6954, 38.7632), 
        marker = {
            id = 21,
            jump = true,
            faceCamera = false,
            rotate = true,
            size = {
                x = 0.5,
                y = 1.0,
                z = 0.5,
            },
            color = {
                r = 0,
                g = 0,
                b = 255,
                a = 75,
            },
        },
        blip = {
            id = 77,
            color = 62,
            size = 0.7,
            text = 'Lifeinvader',
        },
    },
}
local showhelp = false
cl_config.functions = {
    helpNotification = function(key, text, inRange)
        if inRange then
            key = tostring(key or "E")
            text = tostring(text or "Drücke E um zu interagieren")
            TriggerEvent("masora_hud-v2:helpNotify", key, text)
        end
    end,
    notification = function(type, text)
        ESX.ShowNotification(text)
    end,
    ADnotification = function(sender, phoneNumber, message)
        ESX.ShowNotification(sender .. ": " .. message, 10000)
    end,
    getPlayerInfo = function()
        local xPlayer = ESX.GetPlayerData()
        local phoneNumber = exports['roadphone']:getPhoneNumber() or "555-" .. math.random(1000, 9999)
        return {
            name = xPlayer.name or xPlayer.firstName .. ' ' .. xPlayer.lastName,
            phoneNumber = phoneNumber
        }
    end
}