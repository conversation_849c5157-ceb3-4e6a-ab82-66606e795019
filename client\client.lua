ESX = exports["es_extended"]:getSharedObject()


local isUIOpen = false
local lastHelpNotification = 0
local helpNotificationCooldown = 500

function OpenLifeInvaderUI()
    if not isUIOpen then
        isUIOpen = true
        SetNuiFocus(true, true)
        SendNUIMessage({
            type = "open"
        })
    end
end

function CloseLifeInvaderUI()
    if isUIOpen then
        isUIOpen = false
        SetNuiFocus(false, false)
        SendNUIMessage({
            type = "close"
        })
    end
end

RegisterNUICallback('close', function(data, cb)
    CloseLifeInvaderUI()
    cb('ok')
end)

RegisterNUICallback('postAd', function(data, cb)
    if data.isPrivate then
        TriggerServerEvent('lifeinvader:postAd', data.message, "Anonym", "Privat")
    else
        local playerInfo = cl_config.functions.getPlayerInfo()
        TriggerServerEvent('lifeinvader:postAd', data.message, playerInfo.name, playerInfo.phoneNumber)
    end
    cb('ok')
end)

RegisterNUICallback('reportAd', function(data, cb)
    if data.sender and data.phoneNumber and data.message then
        TriggerServerEvent('lifeinvader:reportAd', data.sender, data.phoneNumber, data.message)
    end
    cb('ok')
end)

RegisterNetEvent('lifeinvader:receiveAd')
AddEventHandler('lifeinvader:receiveAd', function(sender, phoneNumber, message)
    if isUIOpen then
        SendNUIMessage({
            type = "newAd",
            sender = sender,
            phoneNumber = phoneNumber,
            message = message
        })
    end
    cl_config.functions.ADnotification(sender, phoneNumber, message)
end)

Citizen.CreateThread(function()
    while true do
        local sleep = 1000
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        
        for _, location in pairs(cl_config.positions) do
            local distance = #(coords - location.position)
            
            if distance < 10.0 then
                sleep = 0
                DrawMarker(
                    location.marker.id,
                    location.position.x, location.position.y, location.position.z - 1.0,
                    0.0, 0.0, 0.0,
                    0.0, 0.0, 0.0,
                    location.marker.size.x, location.marker.size.y, location.marker.size.z,
                    location.marker.color.r, location.marker.color.g, location.marker.color.b, location.marker.color.a,
                    false, true, 2, false, nil, nil, false
                )
                
                if distance < 1.5 then
                    local currentTime = GetGameTimer()
                    if currentTime - lastHelpNotification > helpNotificationCooldown then
                        cl_config.functions.helpNotification("E", "Drücke E um LifeInvader zu öffnen", true)
                        lastHelpNotification = currentTime
                    end
                    
                    if IsControlJustReleased(0, 38) then
                        OpenLifeInvaderUI()
                    end
                end
            end
        end
        
        Citizen.Wait(sleep)
    end
end)
