@import url('https://fonts.cdnfonts.com/css/svn-gilroy?styles=55332,55331');
@import url('https://fonts.cdnfonts.com/css/druk-wide-trial');
@import url('https://fonts.cdnfonts.com/css/druk-trial');
@import url('https://fonts.googleapis.com/css2?family=Abhaya+Libre:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.cdnfonts.com/css/akrobat?styles=16717,28193,28192,28194,28196,28195,28197');
@import url("https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Sen:wght@400;700;800&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.cdnfonts.com/css/karstar-free');
@import url('https://fonts.cdnfonts.com/css/druk-trial');
@import url('https://fonts.cdnfonts.com/css/abhaya-libre-2');
@import url("https://fonts.googleapis.com/css2?family=Oswald:wght@200;300;400;500;600;700&display=swap");
@import url('https://fonts.cdnfonts.com/css/nekst');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.cdnfonts.com/css/bebas-neue');
@import url('https://fonts.cdnfonts.com/css/agency-fb');
@import url('https://gistcdn.githack.com/mfd/09b70eb47474836f25a21660282ce0fd/raw/e06a670afcb2b861ed2ac4a1ef752d062ef6b46b/Gilroy.css');
@import url('https://fonts.cdnfonts.com/css/vcr-osd-mono');
@import url("https://db.onlinewebfonts.com/c/5dc6fc5a874b27ff1c287c19dc30bf1b?family=Arame-Mono");

@font-face {
    font-family: 'Chat';
    src: url('./fonts/chalet.otf');
}

@font-face {
    font-family: 'EasyOpenFace';
    src: url('./fonts/EasyOpenFace/EasyOpenFace.ttf');
}

@font-face {
    font-family: 'Valo';
    src: url('./fonts/Valo/Valo.ttf');
}

@font-face {
    font-family: 'Gilroy-Regular';
    src: url('./fonts/Gilroy-Regular/Gilroy-Regular.ttf');
}

@font-face {
    font-family: 'Avant Garde Book BT';
    font-style: normal;
    font-weight: normal;
    src: local('Avant Garde Book BT'), url('./fonts/Avgardd/AVGARDN_2.woff') format('woff');
}


@font-face {
    font-family: 'Avant Garde Demi BT';
    font-style: normal;
    font-weight: normal;
    src: local('Avant Garde Demi BT'), url('./fonts/Avgardd/AVGARDD_2.woff') format('woff');
}


@font-face {
    font-family: 'Avant Garde Demi Oblique BT';
    font-style: normal;
    font-weight: normal;
    src: local('Avant Garde Demi Oblique BT'), url('./fonts/Avgardd/AVGARDDO_2.woff') format('woff');
}

@font-face {
    font-family: Gilroy123;
    font-style: normal;
    font-weight: 400;
    src: url('./fonts/Gilroy/Gilroy-Regular.ttf');
}

@font-face {
    font-family: Gilroy123;
    font-style: normal;
    font-weight: 500;
    src: url('./fonts/Gilroy/Gilroy-Medium.ttf');
}

@font-face {
    font-family: Gilroy123;
    font-style: normal;
    font-weight: 600;
    src: url('./fonts/Gilroy/Gilroy-SemiBold.ttf');
}

*,
*::before,
*::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-family: var(--ff-header);
    user-select: none;
    color: var(--color-white);
    /* background: url(/img/bg.png);
    background-size: cover; */
}

input {
    border: none;
    outline: none;
    background-color: transparent;
}

:root {
    --ff-header: 'SVN-Gilroy', sans-serif;
    --ff-special: 'Druk Wide Trial', sans-serif;
    --ff-druk: 'Druk Trial', sans-serif;
    --ff-agency: 'Agency FB', sans-serif;
    --border-radius-frame: 1vh;
    --border-radius-close: .5vh;
    --color-white: rgba(255, 255, 255, 1);
    --color-opacity-white: rgba(255, 255, 255, .5);
    --ff-passport: 'Abhaya Libre', serif;
    --ff-rageui: 'Chat';
    --ff-akrobat: 'Akrobat', sans-serif;
    --ff-inter: 'Inter', sans-serif;
    --ff-special: 'Gilroy-Regular', sans-serif;
    --ff-rad: 'Rajdhani', sans-serif;
    --ff-mons: 'Montserrat', sans-serif;
    --ff-bebas: 'Bebas Neue', sans-serif;
    --ff-gilroy: 'Gilroy', sans-serif;
    --ff-valo: 'Valo', Arial, Helvetica, sans-serif;
    --ff-gangwar: 'Avant Garde Demi Oblique BT', sans-serif;
    --ff-gangwar-2: 'Avant Garde Book BT', sans-serif;
    --clr-pink: #BC1156;
    --clr-orange: #EB9857;
    --ff-bit: 'VCR OSD Mono', sans-serif;
    --ff-body: 'Roboto', sans-serif;

    --clr-new-blue: #3266d7;
    --clr-new-blue-2: #453fbd;
    --clr-new-orange: #FF9C23;
    
    --piv: 0.09259259259259259vh;
}

:root {
    --ff-header: 'SVN-Gilroy', sans-serif;
    --ff-special: 'Druk Wide Trial', sans-serif;
    --ff-druk: 'Druk Trial', sans-serif;
    --ff-agency: 'Agency FB', sans-serif;
    --border-radius-frame: 1vh;
    --border-radius-close: .5vh;
    --color-white: rgba(255, 255, 255, 1);
    --color-opacity-white: rgba(255, 255, 255, .5);
    --ff-passport: 'Abhaya Libre', serif;
    --ff-rageui: 'Chat';
    --ff-akrobat: 'Akrobat', sans-serif;
    --ff-inter: 'Inter', sans-serif;
    --ff-special: 'Gilroy-Regular', sans-serif;
    --ff-rad: 'Rajdhani', sans-serif;
    --ff-mons: 'Montserrat', sans-serif;
    --ff-bebas: 'Bebas Neue', sans-serif;
    --ff-gilroy: 'Gilroy', sans-serif;
    --ff-valo: 'Valo', Arial, Helvetica, sans-serif;
    --ff-gangwar: 'Avant Garde Demi Oblique BT', sans-serif;
    --ff-gangwar-2: 'Avant Garde Book BT', sans-serif;
    --clr-pink: #BC1156;
    --clr-orange: #EB9857;
    --ff-bit: 'VCR OSD Mono', sans-serif;
    --ff-body: 'Roboto', sans-serif;
    --clr-new-blue: #3266d7;
    --clr-new-blue-2: #453fbd;
    --clr-new-orange: #FF9C23;
    --piv: 0.09259259259259259vh;
}