ESX = exports["es_extended"]:getSharedObject()


local playerLastMessage = {}

local cooldowns = {}
local config = {
    cooldown = 300,
    adCost = 500
}

function canPostAd(source)
    local currentTime = os.time()
    local lastPostTime = cooldowns[source] or 0
    return (currentTime - lastPostTime) >= config.cooldown
end

RegisterNetEvent('lifeinvader:postAd')
AddEventHandler('lifeinvader:postAd', function(message, sender, phoneNumber)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    if not xPlayer then return end
    
    if not canPostAd(_source) then
        local timeLeft = math.ceil((cooldowns[_source] + config.cooldown - os.time()) / 60)
        TriggerClientEvent('esx:showNotification', _source, 'Du musst noch ' .. timeLeft .. ' Minuten warten, bevor du eine neue Anzeige schalten kannst.')
        return
    end
    
    if xPlayer.getMoney() < config.adCost then
        TriggerClientEvent('esx:showNotification', _source, 'Du hast nicht genug Geld! Du brauchst ' .. config.adCost .. '$.')
        return
    end
    
    xPlayer.removeMoney(config.adCost)
    
    cooldowns[_source] = os.time()
    
    TriggerClientEvent('lifeinvader:receiveAd', -1, sender, phoneNumber, message)
    
    TriggerClientEvent('esx:showNotification', _source, 'Deine Anzeige wurde erfolgreich geschaltet!')
end)

RegisterNetEvent('lifeinvader:reportAd')
AddEventHandler('lifeinvader:reportAd', function(sender, phoneNumber, message)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    if not xPlayer then return end
    
    print(string.format('[LIFEINVADER] Report from %s: Ad by %s (%s) - %s', 
        xPlayer.getName(), sender, phoneNumber, message))
    
    TriggerClientEvent('esx:showNotification', _source, 'Die Anzeige wurde gemeldet!')
end)

AddEventHandler('playerDropped', function()
    playerLastMessage[source] = nil
    cooldowns[source] = nil
end)

RegisterNetEvent('final_lifeinvader:postAd')
AddEventHandler('final_lifeinvader:postAd', function(message, isPrivate)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    if not xPlayer then return end
    
    if not canPostAd(_source) then
        TriggerClientEvent('final_lifeinvader:notification', _source, 'error', 'Du musst noch ' .. math.ceil((cooldowns[_source] - os.time()) / 60) .. ' Minuten warten, bevor du eine neue Anzeige schalten kannst.')
        return
    end
    
    if xPlayer.getMoney() < 500 then
        TriggerClientEvent('final_lifeinvader:notification', _source, 'error', 'Du hast nicht genug Geld! Du brauchst 500$.')
        return
    end
    
    xPlayer.removeMoney(500)
    
    local playerName = xPlayer.getName()
    local currentPhoneNumber = xPlayer.get('phoneNumber')
    
    cooldowns[_source] = os.time() + 300
    
    if isPrivate then
        playerName = "Anonym"
        currentPhoneNumber = "Privat"
    end
    
    TriggerClientEvent('final_lifeinvader:newAd', -1, playerName, currentPhoneNumber, message)
    
    TriggerClientEvent('final_lifeinvader:notification', _source, 'success', 'Deine Anzeige wurde erfolgreich geschaltet!')
end)
