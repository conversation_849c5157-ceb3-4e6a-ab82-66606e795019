<svg width="944" height="713" viewBox="0 0 944 713" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_25_119)">
<rect width="944" height="713" fill="black"/>
<g filter="url(#filter0_f_25_119)">
<ellipse cx="864" cy="897.5" rx="226" ry="182.5" fill="#3983D9"/>
</g>
<g filter="url(#filter1_f_25_119)">
<ellipse cx="611" cy="916" rx="226" ry="201" fill="#0836D9"/>
</g>
<g filter="url(#filter2_f_25_119)">
<ellipse cx="43" cy="864.5" rx="302" ry="243.5" fill="#3971D9"/>
</g>
<g filter="url(#filter3_f_25_119)">
<ellipse cx="216.5" cy="-192.5" rx="347.5" ry="182.5" fill="#3971D9"/>
</g>
<g filter="url(#filter4_f_25_119)">
<ellipse cx="910" cy="676" rx="137" ry="96" fill="#3971D9"/>
</g>
<g filter="url(#filter5_f_25_119)">
<ellipse cx="569.5" cy="749" rx="71.5" ry="51" fill="#112241"/>
</g>
</g>
<defs>
<filter id="filter0_f_25_119" x="-7" y="70" width="1742" height="1655" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="322.5" result="effect1_foregroundBlur_25_119"/>
</filter>
<filter id="filter1_f_25_119" x="-260" y="70" width="1742" height="1692" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="322.5" result="effect1_foregroundBlur_25_119"/>
</filter>
<filter id="filter2_f_25_119" x="-904" y="-24" width="1894" height="1777" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="322.5" result="effect1_foregroundBlur_25_119"/>
</filter>
<filter id="filter3_f_25_119" x="-776" y="-1020" width="1985" height="1655" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="322.5" result="effect1_foregroundBlur_25_119"/>
</filter>
<filter id="filter4_f_25_119" x="363.632" y="170.632" width="1092.74" height="1010.74" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="204.684" result="effect1_foregroundBlur_25_119"/>
</filter>
<filter id="filter5_f_25_119" x="330.464" y="530.464" width="478.073" height="437.073" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="83.7682" result="effect1_foregroundBlur_25_119"/>
</filter>
<clipPath id="clip0_25_119">
<rect width="944" height="713" fill="white"/>
</clipPath>
</defs>
</svg>
