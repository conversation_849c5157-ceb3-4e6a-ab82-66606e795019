body {
  background-color: rgba(0, 0, 0, 0);
  background-image: url('bg2.png');
  background-size: cover;
  background-position: top center;
  background-repeat: no-repeat;
}
@import "https://fonts.cdnfonts.com/css/bebas-neue";
@import "https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap";
.main__genisis-banking-bottom-scroll-sphere {
  position: absolute;
  bottom: -5vh;
  left: -5vh;
  width: 20vh;
  height: 20vh;
  border-radius: 50%;
  background: #c18617;
  filter: blur(10vh);
  z-index: -1;
}
.main__genisis-banking-bottom-scroll-sphere.withdraw {
  background: #d93939;
}
.main__genisis-banking-bottom-scroll-item-info {
  width: 16vh;
}
.main__genisis-banking-bottom-scroll-item-info p {
  font-size: 1.2vh;
  color: var(--color-white);
}
.main__genisis-banking-bottom-scroll-item-info p:nth-last-child(1) {
  color: #ffffff80;
}
.main__genisis-banking-bottom-scroll-item-info.center {
  text-align: center;
}
.main__genisis-banking-bottom-scroll-item-info.right {
  text-align: right;
}
.main__genisis-banking-bottom-scroll-item-info.right p {
  color: #ff9741;
  text-shadow: 0vh 0vh 1.7vh #ffa641;
}
.main__genisis-banking-bottom-scroll-item-info.right p:nth-last-child(1) {
  color: #ffffff80;
  text-shadow: none;
}
.main__genisis-banking-bottom-scroll-item-border {
  position: absolute;
  bottom: 0vh;
  left: 0vh;
  width: 100%;
  height: 0.1vh;
  background: linear-gradient(to right, rgba(0, 0, 0, 0), #3971d9, rgba(0, 0, 0, 0));
}
.main__genisis-header-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.main__genisis-header-left-stripe {
  width: 0.1vh;
  height: 3.5vh;
  background: rgba(255, 255, 255, 0.5);
  margin-right: 0.5vh;
}
.main__genisis-header-left {
  font-family: var(--ff-arame);
  font-size: 1.5vh;
}
.main__genisis-header-left p:first-child {
  font-weight: 100;
  color: #ffffff80;
  letter-spacing: 0.5vh;
  line-height: 1.5vh;
}
.main__genisis-header-left P:last-child {
  font-weight: 700;
  font-size: 2.6vh;
  color: #fff;
  line-height: 2.6vh;
}
.main__genisis-header-container-right {
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__genisis-header-container-right-back-container {
  width: 3.5vh;
  height: 3.5vh;
  border-radius: var(--border-radius-close);
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(184, 28, 37, 0) 100%);
  box-shadow: 0 0 3.7vh #ffffff26 inset;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-white);
  font-size: 1.5vh;
  cursor: pointer;
  transition: 0.2s ease-in;
}
.main__genisis-header-container-right-back-container i {
  transition: 0.2s ease-in;
}
.main__genisis-header-container-right-back-container:hover {
  box-shadow: 0 0 1.7vh #ffffff80 inset;
}
.main__genisis-header-container-right-back-container:hover i {
  transform: rotateY(180deg);
}
.main__genisis-header-container-right-close-container {
  width: 3.5vh;
  height: 3.5vh;
  border-radius: var(--border-radius-close);
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(184, 28, 37, 0) 100%);
  box-shadow: 0 0 3.7vh #ffffff26 inset;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-white);
  font-size: 1.5vh;
  cursor: pointer;
  transition: 0.2s ease-in;
}
.main__genisis-header-container-right-close-container i {
  transition: 0.2s ease-in;
}
.main__genisis-header-container-right-close-container:hover {
  box-shadow: 0 0 1.7vh #ffffff80 inset;
}
.main__genisis-header-container-right-close-container:hover i {
  transform: rotateY(180deg);
}
.main__genisis-header-bg {
  position: absolute;
  top: 2vh;
  left: 3vh;
}
.main__genisis-header-bg img {
  width: 6vh;
}
.main__genisis-garage-flex-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5vh;
  overflow: hidden;
}
.main__genisis-garage-container {
  width: 90vh;
  border-radius: var(--border-radius-frame);
  background: url(images/garage/bg.png);
  background-size: cover;
  padding: 3vh;
  overflow: hidden;
}
.main__genisis-garage-scroll-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1vh;
  height: 49vh;
  overflow-y: scroll;
  margin-top: 3vh;
  padding-right: 1vh;
}
.main__genisis-garage-scroll-container::-webkit-scrollbar {
  width: 0.2vh;
}
.main__genisis-garage-scroll-container::-webkit-scrollbar-thumb {
  width: 0.2vh;
  background: rgba(255, 255, 255, 0.05);
}
.main__genisis-garage-scroll-container::-webkit-scrollbar-thumb {
  width: 0.2vh;
  background: #c19117;
  box-shadow: 0 0 1.7vh #c19117;
}
.main__genisis-garage-scroll-item {
  position: relative;
  width: 100%;
  height: 24vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 2.5vh #ffffff26 inset;
  padding: 1vh;
  border-radius: 0.4vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.main__genisis-garage-scroll-item-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.main__genisis-garage-scroll-item-header-left p:nth-child(1) {
  font-size: 2vh;
  font-family: var(--ff-druk);
  text-transform: uppercase;
  background: radial-gradient(539.11% 270.5% at 50.17% 50%, #c19117 0%, rgba(217, 174, 57, 0) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.main__genisis-garage-scroll-item-header-left p:nth-child(2) {
  font-size: 1.2vh;
  color: var(--color-white);
}
.main__genisis-garage-scroll-item-header-right i {
  color: #ffffff40;
  cursor: pointer;
  transition: 0.2s ease-in;
  font-size: 1.5vh;
}
.main__genisis-garage-scroll-item-header-right:hover i,
.main__genisis-garage-scroll-item-header-right.active i {
  color: #ffcc48;
  text-shadow: 0vh 0vh 1.7vh #ffcc48;
}
.main__genisis-garage-scroll-item-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 13vh;
}
.main__genisis-garage-scroll-item-img img {
  width: 100%;
  height: 100%;
}
.main__genisis-garage-scroll-btn {
  position: absolute;
  bottom: 1.05vh;
  width: 17.8vh;
  height: 3.8vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-transform: uppercase;
  font-size: 1.2vh;
  color: var(--color-white);
  border-radius: 0.25vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 0 3.7vh #ffffff26 inset,
    0 0.4vh 5.6vh #ffffff26;
  padding: 1vh;
  cursor: pointer;
  transition:
    border 0.2s ease-in,
    box-shadow 0.2s ease-in,
    background-color 0.2s ease-in;
}
.main__genisis-garage-scroll-btn:hover {
  box-shadow:
    0 0 3vh #fff inset,
    0 0 1.7vh #c19117;
  background: #9c6f14;
  border: 0.1vh solid transparent;
}
.main__genisis-header-btn-container {
  position: absolute;
  top: 3.25vh;
  right: 8vh;
  display: flex;
  gap: 1vh;
}
.main__genisis-header-btn-1 {
  display: flex;
  justify-content: center;
  padding: 1vh;
  align-items: center;
  border-radius: 0.4vh;
  height: 3.5vh;
  border: 0.1vh solid #c19117;
  background: linear-gradient(0deg, #9c6f14 0%, rgba(255, 125, 69, 0) 100%);
  box-shadow:
    0 0 3.7vh #a37a09 inset,
    0 0.4vh 5.6vh #ff934540;
  font-size: 1.2vh;
  color: var(--color-white);
  text-transform: uppercase;
  cursor: pointer;
  transition: 0.2s ease-in;
}
.main__genisis-header-btn-1:hover,
.main__genisis-header-btn-1.active {
  box-shadow: 0 0 1.7vh #9c6f14 inset;
}
.main__genisis-header-btn-2 {
  height: 3.5vh;
  padding: 1vh;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.4vh;
  border: 0.1vh solid #d939d9;
  background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(190, 57, 217, 0.25) 0%, rgba(206, 57, 217, 0) 100%);
  box-shadow:
    0 0 3.7vh #d939d9 inset,
    0 0.4vh 5.6vh #b439d940;
  font-size: 1.2vh;
  color: var(--color-white);
  text-transform: uppercase;
  transition: 0.2s ease-in;
  cursor: pointer;
}
.main__genisis-header-btn-2:hover,
.main__genisis-header-btn-2.active {
  box-shadow: 0 0 1.7vh #d939d9 inset;
}
.main__genisis-header-btn-3 {
  position: relative;
  height: 3.5vh;
  padding-left: 1vh;
  padding-right: 1vh;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.4vh;
  border: 0.1vh solid #ffcc48;
  background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(217, 174, 57, 0.25) 0%, rgba(217, 174, 57, 0) 100%);
  box-shadow:
    0 0 3.7vh #ff9e45 inset,
    0 0.4vh 5.6vh #d9ae3940;
  font-size: 1.2vh;
  color: var(--color-white);
  text-transform: uppercase;
  transition: 0.2s ease-in;
  cursor: pointer;
  gap: 0.5vh;
  overflow: hidden;
}
.main__genisis-header-btn-3 p {
  z-index: 100;
}
.main__genisis-header-btn-3-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3vh;
  color: #744519;
  transition: 0.2s ease-in;
  z-index: 0;
  opacity: 0.25;
}
.main__genisis-header-btn-3:hover .main__genisis-header-btn-3-bg,
.main__genisis-header-btn-3.active .main__genisis-header-btn-3-bg {
  transform: translate(-50%, -50%) scale(2);
  color: #8f541e;
}
.main__genisis-garage-scroll-item-header-right-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1vh;
}
.main__genisis-garage-scroll-item-return-icon {
  position: absolute;
  top: 2vh;
  right: 2vh;
  color: #ffffff40;
  cursor: pointer;
  font-size: 1.6vh;
  transition: 0.2s ease-in;
}
.main__genisis-garage-scroll-item-return-icon:hover {
  color: #ff4848;
  text-shadow: 0vh 0vh 1.7vh #ff4848;
}
.main__genisis-garage-scroll-item-rename-header {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 3vh;
}
.main__genisis-garage-scroll-item-rename-header-icon {
  font-size: 3.25vh;
  text-transform: uppercase;
  color: #ffcc48;
  text-shadow: 0vh 0vh 1.7vh #ffcc48;
}
.main__genisis-garage-scroll-item-rename-header-small {
  font-size: 1.2vh;
  text-transform: uppercase;
  color: #ffffff8c;
  font-family: var(--ff-inter);
  font-weight: 300;
  margin-top: 1vh;
}
.main__genisis-garage-scroll-item-rename-input-container {
  position: relative;
  left: 50%;
  transform: translate(-50%);
  width: 16vh;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.8vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1vh;
  margin-top: 1.75vh;
}
.main__genisis-garage-scroll-item-rename-input-container input {
  background: none;
  outline: none;
  border: none;
  width: 10vh;
  font-family: var(--ff-inter);
  font-size: 1.3vh;
  text-align: center;
  color: #ffffff40;
}
.main__genisis-garage-scroll-item-rename-input-container input::placeholder {
  color: #ffffff40;
}
.main__genisis-garage-item-window-2,
.main__genisis-garage-item-window-1 {
  position: relative;
  height: 100%;
}
.main__genisis-garage-scroll-item-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 1.05vh;
  width: 17.8vh;
  height: 3.8vh;
  text-transform: uppercase;
  font-size: 1.2vh;
  color: var(--color-white);
  border-radius: 0.25vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 0 3.7vh #ffffff26 inset,
    0 0.4vh 5.6vh #ffffff26;
  padding: 1vh;
  cursor: pointer;
  transition: 0.2s ease-in;
}
.main__genisis-garage-scroll-item-btn:hover {
  box-shadow:
    0 0 3vh #fff inset,
    0 0 1.7vh #c19117;
  background: #9c6f14;
  border: 0.1vh solid transparent;
}
.main__genisis-lifeinvader-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80vh;
  padding: 3vh;
  border-radius: var(--border-radius-frame);
  background: url(images/lifeinvader/bg.png);
  background-size: cover;
  background-position: center;
  overflow: hidden;
}
.main__genisis-lifeinvader-grid {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 0.5fr;
  gap: 1.5vh;
  margin-top: 2vh;
}
.main__genisis-lifeinvader-grid-middle-item {
  position: relative;
  width: 100%;
  height: 40.25vh;
  padding: 1vh;
  border-radius: 0.4vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 1.7vh #ffffff26 inset;
  overflow: hidden;
}
.main__genisis-lifeinvader-grid-middle-item-header {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  margin-top: 3vh;
}
.main__genisis-lifeinvader-grid-middle-item-header-text {
  font-family: var(--ff-arame);
  font-size: 3vh;
  font-weight: 700;
  color: #17c164;
  text-shadow: 0vh 0vh 1.7vh #17c164;
  text-transform: uppercase;
}
.main__genisis-lifeinvader-grid-middle-item-header p:nth-last-child(1) {
  font-family: var(--ff-header);
  font-size: 1.2vh;
  font-weight: 400;
  color: #ffffff80 !important;
  width: 75%;
}
.main__genisis-lifeinvader-grid-item-middle-bg {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  width: 100%;
  height: 13vh;
  border-radius: 100%;
  background: rgba(255, 255, 255, 0.15);
  filter: blur(8.3vh);
  top: -10vh;
  z-index: -1;
}
.main__genisis-lifeinvader-grid-item-middle-bottom-bg {
  position: absolute;
  width: 100%;
  height: 45vh;
  border-radius: 100%;
  left: 50%;
  transform: translate(-50%);
  bottom: -30.5vh;
  background: rgba(255, 255, 255, 0.25);
  filter: blur(32.5vh);
  z-index: -1;
}
.main__genisis-lifeinvader-grid-item-middle-text-wrapper {
  position: absolute;
  bottom: 1vh;
  right: 1vh;
  display: flex;
  align-items: center;
  gap: 0.5vh;
}
.main__genisis-lifeinvader-grid-item-middle-text-count {
  font-size: 1.2vh;
  font-weight: 400;
  color: var(--color-white);
  padding: 0.5vh 1vh;
  border-radius: 0.2vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 1.7vh #ffffff26 inset;
}
.main__genisis-lifeinvader-grid-item-middle-text-count.green p span {
  color: #17c164;
  text-shadow: 0vh 0vh 1.7vh #17c164;
}
.main__genisis-lifeinvader-grid-item-middle-textarea textarea {
  width: 100%;
  height: 48vh;
  background: none;
  outline: none;
  margin-top: 2vh;
  resize: none;
  color: #ffffff80;
  border: none;
  font-family: var(--ff-header);
}
.main__genisis-lifeinvader-grid-middle-item-btn-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1vh;
  margin-top: 1vh;
}
.main__genisis-lifeinvader-grid-middle-item-btn-left {
  width: 100%;
  padding: 1vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 1.2vh;
  text-transform: uppercase;
  color: var(--color-white);
  border-radius: 0.2vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 1.7vh #ffffff26 inset;
  cursor: pointer;
  transition: 0.2s ease-in;
}
.main__genisis-lifeinvader-grid-middle-item-btn-left:hover {
  box-shadow:
    0 0 3vh #03381b inset,
    0 0 1.7vh #17c164;
  background: #149c51;
  border: 0.1vh solid transparent;
}
.main__genisis-lifeinvader-grid-middle-item-btn-right {
  width: 100%;
  padding: 1vh;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 1.2vh;
  text-transform: uppercase;
  color: var(--color-white);
  border-radius: 0.2vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 1.7vh #ffffff26 inset;
  cursor: pointer;
  transition: 0.2s ease-in;
}
.main__genisis-lifeinvader-grid-middle-item-btn-right:hover {
  box-shadow:
    0 0 3vh #631a1a inset,
    0 0 1.7vh #d93939;
  background: #991f1f;
  border: 0.1vh solid transparent;
}
.main__genisis-lifeinvader-grid-right-item-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 45vh;
  gap: 0.5vh;
  overflow-y: scroll;
  padding-right: 1vh;
}
.main__genisis-lifeinvader-grid-right-item-container::-webkit-scrollbar {
  width: 0.3vh;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5vh;
}
.main__genisis-lifeinvader-grid-right-item-container::-webkit-scrollbar-thumb {
  width: 0.3vh;
  background: #3971d9;
  border-radius: 5vh;
}
.main__genisis-lifeinvader-grid-right-item {
  position: relative;
  width: 95%;
  padding: 1vh;
  overflow: hidden;
  gap: 0.5vh;
  border-radius: 0.4vh;
  border: 0.1vh solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 1.7vh #ffffff26 inset;
}
.main__genisis-lifeinvader-grid-right-item-right-header {
  font-size: 1.3vh;
  color: var(--color-white);
}
.main__genisis-lifeinvader-grid-right-item-right-text {
  font-size: 1.2vh;
  color: #ffffff80;
  padding-top: 0.25vh;
}
.main__genisis-lifeinvader-grid-right-item-right-text p {
  word-break: break-all;
}
.main__genisis-lifeinvader-grid-right-item-right-call {
  display: flex;
  align-items: center;
  gap: 0.5vh;
  margin-top: 0.5vh;
}
.main__genisis-lifeinvader-grid-right-item-right-call-icon {
  width: 2.5vh;
  height: 2.5vh;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.3vh;
  background: radial-gradient(96% 96% at 50% 50%, rgba(57, 217, 92, 0.3) 0%, rgba(57, 113, 217, 0) 100%);
  color: #17c164;
  font-size: 1.2vh;
}
.main__genisis-lifeinvader-grid-right-item-right-call-text {
  height: 2.5vh;
  font-size: 1.2vh;
  color: var(--color-white);
  border-radius: 0.3vh;
  background: radial-gradient(1278.19% 193.79% at 0% 6%, rgba(57, 113, 217, 0.3) 0%, rgba(57, 113, 217, 0) 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0vh 1vh;
}
.main__genisis-lifeinvader-grid-right-item-right-call-text p {
  margin-top: -0.25vh;
}
.main__genisis-lifeinvader-grid-right-item-left-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
.main__genisis-lifeinvader-grid-right-item-left-container img {
  width: 5vh;
}
.main__v3-hud-lifeinvader-container {
  position: absolute;
  bottom: 25vh;
  left: 2vh;
  width: 25vh;
}
.main__v3-hud-lifeinvader-item {
  width: 100%;
}
.main__v3-hud-lifeinvader-top-header {
  display: flex;
  align-items: center;
  gap: 1vh;
}
.main__v3-hud-lifeinvader-header-container {
  width: 100%;
  padding: 0.5vh;
  background: linear-gradient(to right, rgba(254, 70, 70, 1), rgba(255, 255, 255, 0));
  border-radius: 1vh 1vh 0vh 0vh;
}
.main__v3-hud-lifeinvader-icon img {
  width: 3.25vh;
}
.main__v3-hud-lifeinvader-header-right {
  margin-top: 0.75vh;
}
.main__v3-hud-lifeinvader-header-big {
  font-size: 1.5vh;
  text-transform: uppercase;
  font-weight: 700;
  margin-top: -0.5vh;
}
.main__v3-hud-lifeinvader-header-small {
  font-size: 1vh;
  font-weight: 500;
  margin-top: -0.25vh;
  margin-bottom: 0.5vh;
}
.main__v3-hud-lifeinvader-bottom-header {
  width: 100%;
  background: linear-gradient(to right, #bf2a2a, rgba(255, 255, 255, 0));
  padding: 0.5vh;
  border-radius: 0.5vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.main__v3-hud-lifeinvader-bottom-header-left {
  display: flex;
  align-items: center;
  gap: 1vh;
  font-size: 1vh;
}
.main__v3-hud-lifeinvader-bottom-header-right {
  font-size: 1vh;
}
.main__v3-hud-lifeinvader-bottom-header-left-icon {
  width: 2.5vh;
  height: 2.5vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 0.5vh;
  font-size: 1.2vh;
}