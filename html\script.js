let isVisible = false;

document.querySelector('.main__genisis-lifeinvader-container').style.display = "none";

window.addEventListener('message', function(event) {
    let data = event.data;
    
    if (data.type === "open") {
        document.querySelector('.main__genisis-lifeinvader-container').style.display = "block";
        isVisible = true;
    } else if (data.type === "close") {
        document.querySelector('.main__genisis-lifeinvader-container').style.display = "none";
        isVisible = false;
    }
});

document.querySelector('.main__genisis-header-container-right-close-container').addEventListener('click', function() {
    fetch(`https://${GetParentResourceName()}/close`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    });
});

document.querySelector('.main__genisis-lifeinvader-grid-middle-item-btn-left').addEventListener('click', function() {
    const messageInput = document.querySelector('.main__genisis-lifeinvader-grid-item-middle-textarea textarea');
    const message = messageInput.value.trim();
    
    if (message) {
        fetch(`https://${GetParentResourceName()}/postAd`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                isPrivate: false
            })
        });
        messageInput.value = '';
        updateCharacterCount(0);
    }
});

document.querySelector('.main__genisis-lifeinvader-grid-middle-item-btn-right').addEventListener('click', function() {
    const messageInput = document.querySelector('.main__genisis-lifeinvader-grid-item-middle-textarea textarea');
    const message = messageInput.value.trim();
    
    if (message) {
        fetch(`https://${GetParentResourceName()}/postAd`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                isPrivate: true
            })
        });
        messageInput.value = '';
        updateCharacterCount(0);
    }
});

const textarea = document.querySelector('.main__genisis-lifeinvader-grid-item-middle-textarea textarea');
const charCount = document.querySelector('.main__genisis-lifeinvader-grid-item-middle-text-count p');

function updateCharacterCount(count) {
    charCount.textContent = `${count}/400 Zeichen`;
}

textarea.addEventListener('input', function() {
    const count = this.value.length;
    updateCharacterCount(count);
});

function reportAd(sender, phoneNumber, message) {
    fetch(`https://${GetParentResourceName()}/reportAd`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            sender: sender,
            phoneNumber: phoneNumber,
            message: message
        })
    });
}

function addAdToFeed(sender, phoneNumber, message) {
    const feed = document.querySelector('.main__genisis-lifeinvader-grid-right-item-container');
    
    const adElement = document.createElement('div');
    adElement.className = 'main__genisis-lifeinvader-grid-right-item';
    
    adElement.innerHTML = `
        <div class="main__genisis-lifeinvader-grid-right-item-right-container">
            <div class="main__genisis-lifeinvader-grid-right-item-right-header">
                <p>${sender}</p>
            </div>
            <div class="main__genisis-lifeinvader-grid-right-item-right-text">
                <p>${message}</p>
            </div>
            <div class="main__genisis-lifeinvader-grid-right-item-right-call">
                <div class="main__genisis-lifeinvader-grid-right-item-right-call-icon">
                    <i class="fa-solid fa-phone" aria-hidden="true"></i>
                </div>
                <div class="main__genisis-lifeinvader-grid-right-item-right-call-text">
                    <p>${phoneNumber}</p>
                </div>
            </div>
        </div>
    `;
    
    feed.insertBefore(adElement, feed.firstChild);
}

window.addEventListener('message', function(event) {
    let data = event.data;
    
    if (data.type === "newAd") {
        addAdToFeed(data.sender, data.phoneNumber, data.message);
    }
});
